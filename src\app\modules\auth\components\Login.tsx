import {useState} from 'react'
import * as Yup from 'yup'
import clsx from 'clsx'
import {useFormik} from 'formik'
import {forgetVerifyOtp, getUserByToken, login, verifyEmployeeMobile, verifyEmployeeOtp} from '../core/_requests'
import {useAuth} from '../core/Auth'
import axios, {AxiosError} from 'axios'
import {toast} from 'react-toastify'

const corporateLoginSchema = Yup.object().shape({
    name: Yup.string()
        .min(3, 'Minimum 3 symbols')
        .max(50, 'Maximum 50 symbols')
        .required('Name is required'),
    password: Yup.string()
        .min(3, 'Minimum 3 symbols')
        .max(50, 'Maximum 50 symbols')
        .required('Password is required'),
})

const initialCorporateValues = {
    name: '',
    password: '',
    type: 'corporate',
}

const initialEmployeeValues = {
    mobileNumber: '',
    password: '',
    otp: '',
    confirmPassword: '',
}

const validateEmployee = (
    values: typeof initialEmployeeValues,
    employeeStep: 'mobile' | 'password' | 'otp',
    isForgot: boolean
) => {
    const errors: { [key: string]: string } = {}

    // Always validate mobile number
    if (!values.mobileNumber) {
        errors.mobileNumber = 'Mobile Number is required'
    }

    // Only validate other fields if not in forgot password flow
    if (!isForgot) {
        if (employeeStep === 'password') {
            if (!values.password) {
                errors.password = 'Password is required'
            } else if (values.password.length < 6) {
                errors.password = 'Minimum 6 characters required'
            }
        }
        if (employeeStep === 'otp') {
            if (!values.otp) {
                errors.otp = 'OTP is required'
            }
            if (!values.password) {
                errors.password = 'Password is required'
            } else if (values.password.length < 6) {
                errors.password = 'Minimum 6 characters required'
            }
            if (!values.confirmPassword) {
                errors.confirmPassword = 'Confirm Password is required'
            } else if (values.confirmPassword !== values.password) {
                errors.confirmPassword = 'Passwords must match'
            }
        }
    }

    return errors
}

export function Login() {
    const [loading, setLoading] = useState(false)
    const {saveAuth, setCurrentUser} = useAuth()
    const [activeTab, setActiveTab] = useState<'corporate' | 'employee'>('corporate')
    const [employeeStep, setEmployeeStep] = useState<'mobile' | 'password' | 'otp'>('mobile')
    const [isForgot, setIsForgot] = useState(false)
    const [showCorporatePassword, setShowCorporatePassword] = useState(false)
    const [showEmployeePassword, setShowEmployeePassword] = useState(false)
    const [showEmployeeNewPassword, setShowEmployeeNewPassword] = useState(false)
    const [showEmployeeConfirmPassword, setShowEmployeeConfirmPassword] = useState(false)

    const corporateFormik = useFormik({
        initialValues: initialCorporateValues,
        validationSchema: corporateLoginSchema,
        onSubmit: async (values, {setStatus, setSubmitting}) => {
            setLoading(true)
            try {
                const {data: auth} = await login(values.name, values.password, values.type)
                saveAuth(auth)
                const {data: user} = await getUserByToken(auth.api_token)
                setCurrentUser(user)
            } catch (error) {
                handleError(error, setStatus)
                setSubmitting(false)
                setLoading(false)
            }
        },
    })

    const employeeFormik = useFormik({
        initialValues: initialEmployeeValues,
        validate: (values) => validateEmployee(values, employeeStep, isForgot),
        onSubmit: async (values, {setStatus, setSubmitting}) => {
            if (isForgot) {
                setStatus('')
                setLoading(true)
                try {
                    const {data: response} = await forgetVerifyOtp({
                        mobileno: values.mobileNumber,
                    })
                    if (response.success) {
                        toast.success(response.message)
                        setEmployeeStep('otp')
                        setIsForgot(false)  // Reset forgot password state
                    } else if (response.validation_error) {
                        let errorMessage = ''
                        for (const key in response.data) {
                            if (Object.prototype.hasOwnProperty.call(response.data, key)) {
                                errorMessage += response.data[key].join(', ') + ' '
                            }
                        }
                        setStatus(errorMessage.trim())
                    }
                } catch (error) {
                    handleError(error, setStatus)
                } finally {
                    setSubmitting(false)
                    setLoading(false)
                }
                return
            }

            if (employeeStep === 'mobile') {
                setStatus('')
                setLoading(true)
                try {
                    const {data: response} = await verifyEmployeeMobile({name: values.mobileNumber})
                    if (response.status === 1) {
                        toast.success(response.message)
                        setEmployeeStep('password')
                    } else if (response.status === 2) {
                        toast.success(response.message)
                        setEmployeeStep('otp')
                    } else {
                        handleValidationError(response, setStatus)
                    }
                } catch (error) {
                    handleError(error, setStatus)
                } finally {
                    setSubmitting(false)
                    setLoading(false)
                }
            } else if (employeeStep === 'password') {
                setStatus('')
                setLoading(true)
                try {
                    const {data: auth} = await login(values.mobileNumber, values.password, 'employee')
                    toast.success('Employee login successful')
                    saveAuth(auth)
                    const {data: user} = await getUserByToken(auth.api_token)
                    setCurrentUser(user)
                } catch (error) {
                    handleError(error, setStatus)
                } finally {
                    setSubmitting(false)
                    setLoading(false)
                }
            } else if (employeeStep === 'otp') {
                setStatus('')
                setLoading(true)
                try {
                    const {data: response} = await verifyEmployeeOtp({
                        name: values.mobileNumber,
                        otp: parseInt(values.otp),
                        password: values.password,
                        confirmed_password: values.confirmPassword,
                    })
                    if (response.success) {
                        toast.success(response.message)
                        setEmployeeStep('password')
                    } else {
                        handleValidationError(response, setStatus)
                    }
                } catch (error) {
                    handleError(error, setStatus)
                } finally {
                    setSubmitting(false)
                    setLoading(false)
                }
            }
        },
    })

    const handleValidationError = (response: any, setStatus: (status: string) => void) => {
        if (response.validation_error) {
            let errorMessage = ''
            for (const key in response.data) {
                if (Object.prototype.hasOwnProperty.call(response.data, key)) {
                    errorMessage += response.data[key].join(', ') + ' '
                }
            }
            setStatus(errorMessage.trim())
        } else {
            setStatus('Unexpected response status.')
        }
    }

    const handleError = (error: unknown, setStatus: (status: string) => void) => {
        if (axios.isAxiosError(error)) {
            const axiosError = error as AxiosError<{ message?: string }>
            if (axiosError.response) {
                console.log('Error data:', axiosError.response.data)
                console.log('Error status:', axiosError.response.status)
                setStatus(axiosError.response.data.message || 'An error occurred')
            } else {
                setStatus('An unexpected error occurred')
            }
        } else {
            console.error(error)
            setStatus('An unexpected error occurred')
        }
        saveAuth(undefined)
    }

    // Render control flags
    const shouldShowPasswordField = !isForgot && employeeStep === 'password'
    const shouldShowOtpFields = !isForgot && employeeStep === 'otp'
    const shouldShowForgotLink = !isForgot && employeeStep !== 'otp'

    return (
        <div className="w-100">
            <div className="text-center mb-11">
                <h1 className="text-gray-900 fw-bolder mb-3">Sign In</h1>
            </div>

            {/* Tab Selection */}
            <div className="d-flex justify-content-center mb-5">
                <button
                    type="button"
                    className={clsx('btn btn-flex btn-outline', {
                        'btn-outline-primary active': activeTab === 'corporate',
                        'btn-outline-secondary': activeTab !== 'corporate',
                    })}
                    onClick={() => setActiveTab('corporate')}
                >
                    Corporate
                </button>
                <button
                    type="button"
                    className={clsx('btn btn-flex btn-outline ms-5', {
                        'btn-outline-primary active': activeTab === 'employee',
                        'btn-outline-secondary': activeTab !== 'employee',
                    })}
                    onClick={() => setActiveTab('employee')}
                >
                    Employee
                </button>
            </div>

            {/* Corporate Form */}
            {activeTab === 'corporate' && (
                <form
                    className="form w-100"
                    onSubmit={corporateFormik.handleSubmit}
                    noValidate
                    id="kt_login_signin_form_corporate"
                >
                    {corporateFormik.status && (
                        <div className="mb-lg-15 alert alert-danger">
                            <div className="alert-text font-weight-bold">{corporateFormik.status}</div>
                        </div>
                    )}
                    <div className="fv-row mb-8">
                        <input
                            placeholder="Username"
                            {...corporateFormik.getFieldProps('name')}
                            className={clsx(
                                'form-control',
                                {
                                    'is-invalid': corporateFormik.touched.name && corporateFormik.errors.name,
                                },
                                {
                                    'is-valid': corporateFormik.touched.name && !corporateFormik.errors.name,
                                }
                            )}
                            type="text"
                            name="name"
                            autoComplete="off"
                        />
                        {corporateFormik.touched.name && corporateFormik.errors.name && (
                            <div className="fv-plugins-message-container invalid-feedback">
                                <span role="alert">{corporateFormik.errors.name}</span>
                            </div>
                        )}
                    </div>
                    <div className="fv-row mb-8">
                        <div className="position-relative">
                            <input
                                placeholder="Password"
                                type={showCorporatePassword ? 'text' : 'password'}
                                autoComplete="off"
                                {...corporateFormik.getFieldProps('password')}
                                className={clsx(
                                    'form-control pe-10',
                                    {
                                        'is-invalid': corporateFormik.touched.password && corporateFormik.errors.password,
                                    },
                                    {
                                        'is-valid':
                                            corporateFormik.touched.password &&
                                            !corporateFormik.errors.password &&
                                            corporateFormik.values.password !== ''
                                    }
                                )}
                            />
                            <button
                                type="button"
                                className="btn btn-sm position-absolute top-50 end-0 translate-middle-y"
                                onClick={() => setShowCorporatePassword(!showCorporatePassword)}
                                style={{
                                    background: 'none',
                                    border: 'none',
                                    padding: '3rem 2.5rem',
                                    zIndex: 2
                                }}
                            >
                                <i
                                    className={`fas ${showCorporatePassword ? 'fa-eye-slash' : 'fa-eye'} text-gray-500`}
                                    style={{ fontSize: '1.1rem' }}
                                ></i>
                            </button>
                        </div>
                        {corporateFormik.touched.password && corporateFormik.errors.password && (
                            <div className="fv-plugins-message-container invalid-feedback">
                                <div className="fv-help-block">
                                    <span role="alert">{corporateFormik.errors.password}</span>
                                </div>
                            </div>
                        )}
                    </div>
                    <div className="d-grid mb-10">
                        <button
                            type="submit"
                            id="kt_sign_in_submit_corporate"
                            className="btn btn-primary"
                            disabled={corporateFormik.isSubmitting || !corporateFormik.isValid}
                        >
                            {!loading && <span className="indicator-label">Login</span>}
                            {loading && (
                                <span className="indicator-progress" style={{display: 'block'}}>
                                    Please wait...
                                    <span className="spinner-border spinner-border-sm align-middle ms-2"></span>
                                </span>
                            )}
                        </button>
                    </div>
                </form>
            )}

            {/* Employee Form */}
            {activeTab === 'employee' && (
                <form
                    className="form w-100"
                    onSubmit={employeeFormik.handleSubmit}
                    noValidate
                    id="kt_login_signin_form_employee"
                >
                    {employeeFormik.status && (
                        <div className="mb-lg-15 alert alert-danger">
                            <div className="alert-text font-weight-bold">{employeeFormik.status}</div>
                        </div>
                    )}

                    {/* Mobile Number Field (always shown) */}
                    <div className="fv-row mb-8">
                        <input
                            placeholder="Mobile Number"
                            {...employeeFormik.getFieldProps('mobileNumber')}
                            className={clsx(
                                'form-control',
                                {
                                    'is-invalid':
                                        employeeFormik.touched.mobileNumber && employeeFormik.errors.mobileNumber,
                                },
                                {
                                    'is-valid':
                                        employeeFormik.touched.mobileNumber && !employeeFormik.errors.mobileNumber,
                                }
                            )}
                            type="text"
                            name="mobileNumber"
                            autoComplete="off"
                        />
                        {employeeFormik.touched.mobileNumber && employeeFormik.errors.mobileNumber && (
                            <div className="fv-plugins-message-container invalid-feedback">
                                <span role="alert">{employeeFormik.errors.mobileNumber}</span>
                            </div>
                        )}
                    </div>

                    {/* Password field */}
                    {shouldShowPasswordField && (
                        <div className="fv-row mb-8">
                            <div className="position-relative">
                                <input
                                    placeholder="Password"
                                    {...employeeFormik.getFieldProps('password')}
                                    className={clsx(
                                        'form-control pe-10',
                                        {
                                            'is-invalid': employeeFormik.touched.password && employeeFormik.errors.password,
                                        },
                                        {
                                            'is-valid':
                                                employeeFormik.touched.password &&
                                                !employeeFormik.errors.password &&
                                                employeeFormik.values.password !== ''
                                        }
                                    )}
                                    type={showEmployeePassword ? 'text' : 'password'}
                                    name="password"
                                    autoComplete="off"
                                />
                                <button
                                    type="button"
                                    className="btn btn-sm position-absolute top-50 end-0 translate-middle-y"
                                    onClick={() => setShowEmployeePassword(!showEmployeePassword)}
                                    style={{
                                        background: 'none',
                                        border: 'none',
                                        padding: '3rem 2.5rem',
                                        zIndex: 2
                                    }}
                                >
                                    <i
                                        className={`fas ${showEmployeePassword ? 'fa-eye-slash' : 'fa-eye'} text-gray-500`}
                                        style={{ fontSize: '1.1rem' }}
                                    ></i>
                                </button>
                            </div>
                            {employeeFormik.touched.password && employeeFormik.errors.password && (
                                <div className="fv-plugins-message-container invalid-feedback">
                                    <span role="alert">{employeeFormik.errors.password}</span>
                                </div>
                            )}
                        </div>
                    )}

                    {/* OTP and password fields */}
                    {shouldShowOtpFields && (
                        <>
                            <div className="fv-row mb-8">
                                <input
                                    placeholder="OTP"
                                    {...employeeFormik.getFieldProps('otp')}
                                    className={clsx(
                                        'form-control',
                                        {
                                            'is-invalid': employeeFormik.touched.otp && employeeFormik.errors.otp,
                                        },
                                        {
                                            'is-valid':
                                                employeeFormik.touched.otp &&
                                                !employeeFormik.errors.otp &&
                                                employeeFormik.values.otp !== ''
                                        }
                                    )}
                                    type="text"
                                    name="otp"
                                    autoComplete="off"
                                />
                                {employeeFormik.touched.otp && employeeFormik.errors.otp && (
                                    <div className="fv-plugins-message-container invalid-feedback">
                                        <span role="alert">{employeeFormik.errors.otp}</span>
                                    </div>
                                )}
                            </div>
                            <div className="fv-row mb-8">
                                <div className="position-relative">
                                    <input
                                        placeholder="New Password"
                                        {...employeeFormik.getFieldProps('password')}
                                        className={clsx(
                                            'form-control pe-10',
                                            {
                                                'is-invalid': employeeFormik.touched.password && employeeFormik.errors.password,
                                            },
                                            {
                                                'is-valid':
                                                    employeeFormik.touched.password &&
                                                    !employeeFormik.errors.password &&
                                                    employeeFormik.values.password !== ''
                                            }
                                        )}
                                        type={showEmployeeNewPassword ? 'text' : 'password'}
                                        name="password"
                                        autoComplete="off"
                                    />
                                    <button
                                        type="button"
                                        className="btn btn-sm position-absolute top-50 end-0 translate-middle-y"
                                        onClick={() => setShowEmployeeNewPassword(!showEmployeeNewPassword)}
                                        style={{
                                            background: 'none',
                                            border: 'none',
                                            padding: '3rem 2.5rem',
                                            zIndex: 2
                                        }}
                                    >
                                        <i
                                            className={`fas ${showEmployeeNewPassword ? 'fa-eye-slash' : 'fa-eye'} text-gray-500`}
                                            style={{ fontSize: '1.1rem' }}
                                        ></i>
                                    </button>
                                </div>
                                {employeeFormik.touched.password && employeeFormik.errors.password && (
                                    <div className="fv-plugins-message-container invalid-feedback">
                                        <span role="alert">{employeeFormik.errors.password}</span>
                                    </div>
                                )}
                            </div>
                            <div className="fv-row mb-8">
                                <div className="position-relative">
                                    <input
                                        placeholder="Confirm New Password"
                                        {...employeeFormik.getFieldProps('confirmPassword')}
                                        className={clsx(
                                            'form-control pe-10',
                                            {
                                                'is-invalid':
                                                    employeeFormik.touched.confirmPassword && employeeFormik.errors.confirmPassword,
                                            },
                                            {
                                                'is-valid':
                                                    employeeFormik.touched.confirmPassword &&
                                                    !employeeFormik.errors.confirmPassword &&
                                                    employeeFormik.values.confirmPassword !== ''
                                            }
                                        )}
                                        type={showEmployeeConfirmPassword ? 'text' : 'password'}
                                        name="confirmPassword"
                                        autoComplete="off"
                                    />
                                    <button
                                        type="button"
                                        className="btn btn-sm position-absolute top-50 end-0 translate-middle-y"
                                        onClick={() => setShowEmployeeConfirmPassword(!showEmployeeConfirmPassword)}
                                        style={{
                                            background: 'none',
                                            border: 'none',
                                            padding: '3rem 2.5rem',
                                            zIndex: 2
                                        }}
                                    >
                                        <i
                                            className={`fas ${showEmployeeConfirmPassword ? 'fa-eye-slash' : 'fa-eye'} text-gray-500`}
                                            style={{ fontSize: '1.1rem' }}
                                        ></i>
                                    </button>
                                </div>
                                {employeeFormik.touched.confirmPassword && employeeFormik.errors.confirmPassword && (
                                    <div className="fv-plugins-message-container invalid-feedback">
                                        <span role="alert">{employeeFormik.errors.confirmPassword}</span>
                                    </div>
                                )}
                            </div>
                        </>
                    )}

                    {/* Submit Button */}
                    <div className="d-grid mb-10">
                        <button
                            type="submit"
                            id="kt_sign_in_submit_employee"
                            className="btn btn-primary"
                            disabled={employeeFormik.isSubmitting || !employeeFormik.isValid}
                        >
                            {!loading && (
                                <span className="indicator-label">
                                    {isForgot ? "Send OTP" : "Continue"}
                                </span>
                            )}
                            {loading && (
                                <span className="indicator-progress" style={{display: 'block'}}>
                                    Please wait...
                                    <span className="spinner-border spinner-border-sm align-middle ms-2"></span>
                                </span>
                            )}
                        </button>
                    </div>

                    {/* Forgot Password Link */}
                    {shouldShowForgotLink && (
                        <div className="text-center mt-5">
                            <a
                                className="link-primary fs-6 fw-bold"
                                style={{
                                    cursor: 'pointer'
                                }}
                                onClick={() => {
                                    setIsForgot(true);
                                    setEmployeeStep('mobile');
                                    employeeFormik.resetForm();
                                }}
                            >
                                Forgot Password?
                            </a>
                        </div>
                    )}
                </form>
            )}
        </div>
    )
}