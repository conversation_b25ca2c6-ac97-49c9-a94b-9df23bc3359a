import axios from 'axios';
import {
    CreateSimParams,
    CreateSimResponse,
    DeleteSimParams,
    DeleteSimResponse,
    SimActivePaginationParams,
    SimActivePaginationResponse,
    UpdateSimParams,
    UpdateSimResponse
} from "./_sim_models.ts";


const API_URL = import.meta.env.VITE_APP_API_URL;

export const SIM_ACTIVE_PAGINATION_URL = `${API_URL}/sim/active/pagination`;
export const CREATE_SIM_URL = `${API_URL}/sim`;
export const UPDATE_SIM_URL = `${API_URL}/sim/update`;
export const DELETE_SIM_URL = `${API_URL}/sim/delete`;

export function getSimActivePagination(params: SimActivePaginationParams) {
    return axios.post<SimActivePaginationResponse>(SIM_ACTIVE_PAGINATION_URL, params);
}

export function createSim(params: CreateSimParams) {
    return axios.post<CreateSimResponse>(CREATE_SIM_URL, params);
}

export function updateSim(idCrypt: string, params: UpdateSimParams) {
    return axios.put<UpdateSimResponse>(`${UPDATE_SIM_URL}/${idCrypt}`, params);
}

export function deleteSim(idCrypt: string, params: DeleteSimParams) {
    return axios.put<DeleteSimResponse>(`${DELETE_SIM_URL}/${idCrypt}`, params);
}

const SIM_URL = `${API_URL}/sim`;

export function editSim(simAutoIdCrypt: string) {
    return axios.get(`${SIM_URL}/edit/${simAutoIdCrypt}`);
}

export function downloadSimFormat() {
    return axios.get(`${SIM_URL}/download-format`, {
        responseType: 'blob'
    });
}

export function simDataPreview(formData: FormData) {
    return axios.post(`${SIM_URL}/data-preview`, formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });
}

export function uploadSim(simData: any) {
    return axios.post(`${SIM_URL}/upload`, {
        sim_data: JSON.stringify(simData)
    });
}