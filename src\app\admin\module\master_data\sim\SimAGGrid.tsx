import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {AgGridReact} from 'ag-grid-react';
import {ColDef, GridReadyEvent, ICellRendererParams, IDatasource} from 'ag-grid-community';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import 'ag-grid-community/styles/ag-theme-quartz.css';
import {toast} from 'react-toastify';
import axios from "axios";
import {LoadingOverlay, NoRowsOverlay} from "../../../../Helper/AgGridOverlays";
import CreateSimModal, {CreateModalRef} from "./CreateSimModal";
import DeleteSimModal, {DeleteModalRef} from "./DeleteSimModal";
import ViewSimModal, {ViewModalRef} from "./ViewSimModal";
import EditSimModal, {EditModalRef} from "./EditSimModal";
import UploadSimModal, {UploadModalRef} from "./UploadSimModal";
import {useAuth} from "../../../../modules/auth";
import {SimActivePaginationParams, SimData} from "../../../../modules/auth/core/_sim_models.ts";
import {getSimActivePagination, downloadSimFormat} from "../../../../modules/auth/core/_sim_requests.ts";

const SimAGGrid: React.FC = () => {
    const gridRef = useRef<AgGridReact>(null);
    const createModalRef = useRef<CreateModalRef>(null);
    const deleteModalRef = useRef<DeleteModalRef>(null);
    const viewModalRef = useRef<ViewModalRef>(null);
    const editModalRef = useRef<EditModalRef>(null);
    const uploadModalRef = useRef<UploadModalRef>(null);
    const [loading, setLoading] = useState(true);
    const [rowCount, setRowCount] = useState<number | null>(null);
    const datasourceRef = useRef<IDatasource | null>(null);
    const [failedPages, setFailedPages] = useState<Set<number>>(new Set());
    const {logout} = useAuth();
    const [agGridRefresh, setAgGridRefresh] = useState(false);
    const [selectedSim, setSelectedSim] = useState<SimData | null>(null);
    const [currentPage, setCurrentPage] = useState(1);

    const ActionCellRenderer: React.FC<ICellRendererParams> = (props) => {
        const onView = () => {
            setSelectedSim(props.data);
            if (viewModalRef.current) {
                viewModalRef.current.openModal();
            }
        };

        const onEdit = () => {
            setSelectedSim(props.data);
            if (editModalRef.current) {
                editModalRef.current.openModal();
            }
        };

        const onDelete = () => {
            setSelectedSim(props.data);
            if (deleteModalRef.current) {
                deleteModalRef.current.openModal();
            }
        };

        const isDeleteDisabled = props.data?.SIM_MAP_STATUS === 2;

        return (
            <div className="d-flex align-items-center mt-2 gap-1">

                <button onClick={onEdit} className="btn btn-sm action-btn text-success p-1" title="Edit"
                        style={{width: '36px'}}>
                    <i className="fas fa-edit text-success"></i>
                </button>
                <button onClick={onDelete} className="btn btn-sm action-btn text-danger p-1" title="Delete"
                        style={{width: '36px'}} disabled={isDeleteDisabled}>
                    <i className="fas fa-trash-alt text-danger"></i>
                </button>
                <button onClick={onView} className="btn btn-sm action-btn text-success p-1" title="View"
                        style={{width: '36px'}}>
                    <i className="fas fa-eye text-success"></i>
                </button>
            </div>
        );
    };

    const columnDefs = useMemo<ColDef[]>(() => [

        {field: 'SIM_MOBILE_NO', headerName: 'SIM Mobile No'},
        {field: 'SIM_SERIAL_NO', headerName: 'SIM Serial No'},
        {field: 'SIM_PROVIDER', headerName: 'SIM Provider'},
        {field: 'VEHICLE_REG_NO', headerName: 'Vehicle No'},
        {field: 'CREATED_BY', headerName: 'Created By'},
        {
            field: 'created_on',
            headerName: 'Created On',
            filter: 'agDateColumnFilter',
            filterParams: {
                browserDatePicker: true,
                comparator: (filterDate: Date, cellValue: string) => {
                    if (!cellValue) return 0;
                    const cellDate = new Date(cellValue);
                    if (cellDate < filterDate) return -1;
                    if (cellDate > filterDate) return 1;
                    return 0;
                }
            },
        },
        {
            headerName: 'Actions',
            cellRenderer: ActionCellRenderer,
            sortable: false,
            filter: false,
        },
    ], []);

    const defaultColDef = useMemo<ColDef>(() => ({
        filter: 'agTextColumnFilter',
        filterParams: {
            filterOptions: ['contains'],
            maxNumConditions: 1
        },
        floatingFilter: true,
        sortable: true,
    }), []);

    const getServerSideDatasource = (): IDatasource => {
        return {
            getRows: async (params) => {
                const startRow = params.startRow ?? 0;
                const endRow = params.endRow ?? 100;
                const pageSize = endRow - startRow;
                const page = Math.floor(startRow / pageSize) + 1;
                setCurrentPage(page);
                try {
                    setLoading(true);

                    const paginationParams: SimActivePaginationParams = {
                        page: page.toString(),
                        per_page: pageSize.toString(),
                        filterModel: params.filterModel,
                        orderBy: params.sortModel?.[0]?.colId || '',
                        order: (params.sortModel?.[0]?.sort?.toUpperCase() as 'ASC' | 'DESC') || ''
                    };

                    const response = await getSimActivePagination(paginationParams);

                    const rowData = response.data.sim.data;
                    const totalRows = response.data.sim.total;

                    setRowCount(totalRows);
                    params.successCallback(rowData, totalRows);

                    setFailedPages(prev => {
                        const newSet = new Set(prev);
                        newSet.delete(page);
                        return newSet;
                    });

                } catch (error) {
                    let errorMessage = 'Network error occurred while fetching data. Please use the "Retry Failed Loads" button to attempt recovery.';
                    if (axios.isAxiosError(error)) {
                        const statusCode = error.response?.status;
                        if (statusCode === 403 || statusCode === 401) {
                            logout();
                            errorMessage = 'Your session has expired. Please log in again.';
                        } else if (statusCode === 500) {
                            errorMessage = 'An error occurred while fetching data. Please contact admin.';
                        }
                    }
                    console.error('Error fetching data:', error);
                    toast.error(errorMessage);
                    params.failCallback();
                    setFailedPages(prev => new Set(prev).add(page));
                } finally {
                    setLoading(false);
                }
            }
        };
    };

    const onGridReady = useCallback((params: GridReadyEvent) => {
        const datasource = getServerSideDatasource();
        datasourceRef.current = datasource;
        params.api.setGridOption('datasource', datasource);
    }, []);

    const onBtRetry = useCallback(() => {
        if (gridRef.current && datasourceRef.current) {
            setFailedPages(new Set());
            gridRef.current.api.refreshInfiniteCache();
            gridRef.current.api.paginationGoToPage(0);
        }
    }, []);

    const onBtnExport = useCallback(() => {
        if (gridRef.current && gridRef.current.api) {
            const params = {
                fileName: 'sim_export.csv',
                columnKeys: gridRef.current.api.getColumnDefs()
                    ?.filter((colDef: ColDef) => colDef.field !== 'SIM_ID_crypt' && colDef.headerName !== 'Actions')
                    .map((colDef: ColDef) => colDef.field)
                    .filter((field): field is string => field !== undefined)
            };
            gridRef.current.api.exportDataAsCsv(params);
        }
    }, []);

    const openCreateModal = () => {
        if (createModalRef.current) {
            createModalRef.current.openModal();
        }
    };

    const openUploadModal = () => {
        if (uploadModalRef.current) {
            uploadModalRef.current.openModal();
        }
    };

    const handleDownloadFormat = async () => {
        try {
            const response = await downloadSimFormat();
            
            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', 'sim_master.csv');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);
            
            toast.success('SIM format template downloaded successfully');
        } catch (error) {
            console.error('Error downloading format:', error);
            toast.error('Failed to download SIM format template');
        }
    };

    useEffect(() => {
        if (agGridRefresh && gridRef.current && gridRef.current.api) {
            gridRef.current.api.refreshInfiniteCache();
            setAgGridRefresh(false);
        }
    }, [agGridRefresh]);

    useEffect(() => {
        if (!loading && gridRef.current && gridRef.current.api) {
            try {
                if (rowCount === 0 && currentPage === 1) {
                    gridRef.current.api.showNoRowsOverlay();
                } else {
                    gridRef.current.api.hideOverlay();
                }
            } catch (error) {
                console.error('Error updating grid overlay:', error);
            }
        }
    }, [loading, rowCount, currentPage]);

    return (
        <>
            <div className="row align-items-center mx-1">
                <div className="col-sm">
                    <h1 className="h5 fw-semibold text-body">SIM Management</h1>
                    <p className="mt-2 small text-muted">
                        A list of all SIMs in your account including their details and status.
                    </p>
                </div>

                <div className="col-sm-auto mt-4 mt-sm-0">
                    <button type="button" className="btn btn-primary" onClick={openCreateModal}>
                        Add New SIM
                    </button>
                </div>
                <div className="col-sm-auto mt-4 mt-sm-0">
                    <button type="button" className="btn btn-success" onClick={handleDownloadFormat}>
                        SIM Format
                    </button>
                </div>
                <div className="col-sm-auto mt-4 mt-sm-0">
                    <button type="button" className="btn btn-info" onClick={openUploadModal}>
                        Upload
                    </button>
                </div>
                <div className="col-sm-auto mt-4 mt-sm-0">
                    <button type="button" className="btn btn-primary" onClick={onBtnExport}>
                        Export to CSV
                    </button>
                </div>
                <div className="col-sm-auto mt-4 mt-sm-0">
                    {failedPages.size > 0 && (
                        <button className="btn btn-danger" onClick={onBtRetry}>
                            Retry Failed Loads ({failedPages.size} failed)
                        </button>
                    )}
                </div>
            </div>

            <div className="ag-theme-quartz" style={{height: 600, width: '100%'}}>
                <AgGridReact
                    ref={gridRef}
                    columnDefs={columnDefs}
                    defaultColDef={defaultColDef}
                    rowModelType={'infinite'}
                    pagination={true}
                    paginationPageSize={20}
                    onGridReady={onGridReady}
                    paginationPageSizeSelector={[20, 50, 100000]}
                    noRowsOverlayComponent={NoRowsOverlay}
                    loadingOverlayComponent={LoadingOverlay}
                    loading={loading}
                />
            </div>
            <CreateSimModal
                ref={createModalRef}
                id="createSimModal"
                title="Create SIM"
                setAgGridRefresh={setAgGridRefresh}
            />
            <DeleteSimModal
                ref={deleteModalRef}
                id="deleteSimModal"
                title="Delete SIM"
                sim={selectedSim}
                setAgGridRefresh={setAgGridRefresh}
            />
            <ViewSimModal
                ref={viewModalRef}
                id="viewSimModal"
                title="View SIM Details"
                sim={selectedSim}
            />
            <EditSimModal
                ref={editModalRef}
                id="editSimModal"
                title="Edit SIM"
                sim={selectedSim}
                setAgGridRefresh={setAgGridRefresh}
            />
            <UploadSimModal
                ref={uploadModalRef}
                id="uploadSimModal"
                title="SIM Master Bulk Upload"
                setAgGridRefresh={setAgGridRefresh}
            />
        </>
    );
};

export default SimAGGrid;
