import React, {forwardRef, useImperative<PERSON>andle, useMemo, useRef, useState} from 'react';
import {Modal} from 'bootstrap';
import {AgGridReact} from 'ag-grid-react';
import {ColDef} from 'ag-grid-community';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-quartz.css';
import {toast} from 'react-toastify';
import axios from 'axios';
import {useAuth} from '../../../../modules/auth';
import {getDataForCreate} from '../../../../modules/auth/core/download_template/_download_template_requests';
import {previewDeviceUpload, uploadDevices} from '../../../../modules/auth/core/device_upload/_device_upload_requests';

interface ModalProps {
  id: string;
  title: string;
  setAgGridRefresh: React.Dispatch<React.SetStateAction<boolean>>;
}

export interface UploadDeviceModalRef {
  openModal: () => void;
  closeModal: () => void;
  downloadTemplate: () => void;
}

type PreviewRow = {
  DEVICE_MODEL: string;
  IMEI_NO_1: string;
  IMEI_NO_2?: string;
  errors?: {
    device_model?: boolean;
    imei1?: boolean;
    imei2?: boolean;
    duplicate?: boolean;
  };
};

const UploadDeviceModal = forwardRef<UploadDeviceModalRef, ModalProps>((props, ref) => {
  const {id, title, setAgGridRefresh} = props;
  const modalRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const gridRef = useRef<AgGridReact<PreviewRow>>(null);

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [rows, setRows] = useState<PreviewRow[]>([]);
  // counts removed from UI; compute errors directly from rows
  const {logout} = useAuth();

  const columnDefs = useMemo<ColDef[]>(() => [
    {field: 'DEVICE_MODEL', headerName: 'Device Model'},
    {field: 'IMEI_NO_1', headerName: 'IMEI No 1'},
    {field: 'IMEI_NO_2', headerName: 'IMEI No 2'},
    {
      headerName: 'Errors',
      valueGetter: p => {
        const e = p.data?.errors;
        const errs: string[] = [];
        if (e?.device_model) errs.push('Device Model');
        if (e?.imei1) errs.push('IMEI1');
        if (e?.imei2) errs.push('IMEI2');
        if (e?.duplicate) errs.push('Duplicate');
        return errs.join(', ');
      },
      cellClass: 'text-danger fw-semibold'
    }
  ], []);

  const defaultColDef = useMemo<ColDef>(() => ({
    filter: 'agTextColumnFilter',
    floatingFilter: true,
    sortable: true,
    resizable: true,
  }), []);

  const rowClassRules = useMemo(() => ({
    'bg-light-danger': (params: any) => {
      const e = params.data?.errors;
      return e?.device_model || e?.imei1 || e?.imei2 || e?.duplicate;
    }
  }), []);

  const openModal = () => {
    const modalElement = document.getElementById(id);
    if (modalElement) {
      const modalInstance = Modal.getInstance(modalElement);
      if (modalInstance) {
        modalInstance.show();
      } else {
        new Modal(modalElement).show();
      }
    }
  };

  const closeModal = () => {
    const modalElement = document.getElementById(id);
    if (modalElement) {
      const modalInstance = Modal.getInstance(modalElement);
      if (modalInstance) {
        modalInstance.hide();
      }
    }
  };

  const onPreview = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const file = fileInputRef.current?.files?.[0];
      if (!file) {
        toast.error('Please choose device_master.csv file');
        return;
      }
      if (file.name !== 'device_master.csv') {
        toast.error('Invalid file name. Expected device_master.csv');
        return;
      }
      const formData = new FormData();
      formData.append('file', file);

      const resp = await previewDeviceUpload(formData);
      if (resp.data.success) {
        setRows(resp.data.rows || []);
        toast.success('Preview generated');
      } else {
        toast.error(resp.data.message || 'Preview failed');
      }
    } catch (err) {
      let errorMessage = 'Error while previewing. Please try again.';
      if (axios.isAxiosError(err)) {
        const statusCode = err.response?.status;
        if (statusCode === 403 || statusCode === 401) {
          closeModal();
          logout();
          errorMessage = 'Your session has expired. Please log in again.';
        } else if (statusCode === 500) {
          errorMessage = 'Server error during preview. Contact admin.';
        }
      }
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const onUpload = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const validRows = rows.filter(r => !(r.errors?.device_model || r.errors?.imei1 || r.errors?.imei2 || r.errors?.duplicate));
      if (validRows.length === 0) {
        toast.error('No valid rows to upload');
        return;
      }
      const resp = await uploadDevices({rows: validRows});
      if (resp.data.success) {
        toast.success(resp.data.message || 'Devices uploaded successfully');
        setAgGridRefresh(true);
        setRows([]);
        if (fileInputRef.current) fileInputRef.current.value = '';
        closeModal();
      } else {
        toast.error(resp.data.message || 'Upload failed');
      }
    } catch (err) {
      let errorMessage = 'Error while uploading. Please try again.';
      if (axios.isAxiosError(err)) {
        const statusCode = err.response?.status;
        if (statusCode === 403 || statusCode === 401) {
          closeModal();
          logout();
          errorMessage = 'Your session has expired. Please log in again.';
        } else if (statusCode === 500) {
          errorMessage = 'Server error during upload. Contact admin.';
        }
      }
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const downloadTemplate = async () => {
    try {
      const resp = await getDataForCreate();
      const url = resp.data.deviceFormat || 'https://www.topsa.in/TMS/storage/excel/device_master.csv';
      window.open(url, '_blank');
    } catch {
      window.open('https://www.topsa.in/TMS/storage/excel/device_master.csv', '_blank');
    }
  };

  useImperativeHandle(ref, () => ({
    openModal,
    closeModal,
    downloadTemplate,
  }));

  const hasErrors = rows.some(r => r.errors?.device_model || r.errors?.imei1 || r.errors?.imei2 || r.errors?.duplicate);

  return (
    <div className="modal fade" tabIndex={-1} id={id} ref={modalRef}>
      <div className="modal-dialog modal-xl">
        <div className="modal-content">
          <div className="modal-header">
            <h5 className="modal-title">{title}</h5>
            <button type="button" className="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
          </div>
          <div className="modal-body">
            <div className="mb-3 d-flex align-items-center gap-2">
              <input type="file" ref={fileInputRef} accept=".csv" className="form-control" />
              <button className="btn btn-primary" onClick={onPreview} disabled={isLoading} type="button">
                {isLoading ? 'Please wait...' : 'Preview'}
              </button>
            </div>

            {rows.length > 0 && (
              <div className="ag-theme-quartz" style={{height: 400, width: '100%'}}>
                <AgGridReact
                  ref={gridRef}
                  columnDefs={columnDefs}
                  defaultColDef={defaultColDef}
                  rowData={rows}
                  rowClassRules={rowClassRules}
                />
              </div>
            )}

            {error && (
              <div className="text-danger mt-2">{error}</div>
            )}
          </div>
          <div className="modal-footer">
            <button type="button" className="btn btn-light" data-bs-dismiss="modal">Close</button>
            <button type="button" className="btn btn-success" onClick={onUpload} disabled={rows.length === 0 || hasErrors || isLoading}>
              {isLoading ? 'Uploading...' : 'Upload'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
});

export default UploadDeviceModal;
