import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {AgGridReact} from 'ag-grid-react';
import {ColDef, GridReadyEvent, ICellRendererParams, IDatasource} from 'ag-grid-community';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import 'ag-grid-community/styles/ag-theme-quartz.css';
import {toast} from 'react-toastify';
import axios from "axios";
import {LoadingOverlay, NoRowsOverlay} from "../../../../Helper/AgGridOverlays";
import CreateDeviceModal, {CreateModalRef} from "./CreateDeviceModal";
import UploadDeviceModal, {UploadDeviceModalRef} from "./UploadDeviceModal";
import DeleteDeviceModal, {DeleteModalRef} from "./DeleteDeviceModal";
import ViewDeviceModal, {ViewModalRef} from "./ViewDeviceModal";
import {useAuth} from "../../../../modules/auth";
import {Device, DevicePaginationParams} from "../../../../modules/auth/core/_device_models.ts";
import {getDeviceActivePagination} from "../../../../modules/auth/core/_device_requests.ts";

const DeviceAGGrid: React.FC = () => {
    const gridRef = useRef<AgGridReact>(null);
    const createModalRef = useRef<CreateModalRef>(null);
    const deleteModalRef = useRef<DeleteModalRef>(null);
    const viewModalRef = useRef<ViewModalRef>(null);
    const [loading, setLoading] = useState(true);
    const [rowCount, setRowCount] = useState<number | null>(null);
    const datasourceRef = useRef<IDatasource | null>(null);
    const [failedPages, setFailedPages] = useState<Set<number>>(new Set());
    const {logout} = useAuth();
    const [agGridRefresh, setAgGridRefresh] = useState(false);
    const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
    const uploadModalRef = useRef<UploadDeviceModalRef>(null);
    const [currentPage, setCurrentPage] = useState(1);

    const ActionCellRenderer: React.FC<ICellRendererParams> = (props) => {
        const onView = () => {
            setSelectedDevice(props.data);
            if (viewModalRef.current) {
                viewModalRef.current.openModal();
            }
        };

        const onDelete = () => {
            setSelectedDevice(props.data);
            if (deleteModalRef.current) {
                deleteModalRef.current.openModal();
            }
        };

        const isDeleteDisabled = props.data?.COMPLIANT_STATUS === 2;

        return (
            <div className="d-flex align-items-center mt-2 gap-1">
                <button onClick={onDelete} className="btn btn-sm action-btn text-danger p-1" title="Delete"
                        style={{width: '36px'}} disabled={isDeleteDisabled}>
                    <i className="fas fa-trash-alt text-danger"></i>
                </button>
                <button onClick={onView} className="btn btn-sm action-btn text-primary p-1" title="View"
                        style={{width: '36px'}}>
                    <i className="fas fa-eye text-success"></i>
                </button>
            </div>
        );
    };

    useEffect(() => {
        if (!loading && gridRef.current && gridRef.current.api) {
            try {
                if (rowCount === 0 && currentPage === 1) {
                    gridRef.current.api.showNoRowsOverlay();
                } else {
                    gridRef.current.api.hideOverlay();
                }
            } catch (error) {
                console.error('Error updating grid overlay:', error);
            }
        }
    }, [loading, rowCount, currentPage]);

    const columnDefs = useMemo<ColDef[]>(() => [
        {field: 'DEVICE_MODEL', headerName: 'Device Model'},
        {field: 'IMEI_NO_1', headerName: 'IMEI No 1'},
        {field: 'IMEI_NO_2', headerName: 'IMEI No 2'},
        {field: 'CREATED_BY', headerName: 'Created By'},
        {
            field: 'CREATED_DATE',
            headerName: 'Created At',
            filter: 'agDateColumnFilter',
            filterParams: {
                browserDatePicker: true,
                comparator: (filterDate: Date, cellValue: string) => {
                    if (!cellValue) return 0;
                    const cellDate = new Date(cellValue);
                    if (cellDate < filterDate) return -1;
                    if (cellDate > filterDate) return 1;
                    return 0;
                }
            },
        },
        {
            headerName: 'Actions',
            cellRenderer: ActionCellRenderer,
            sortable: false,
            filter: false,
        },
    ], []);

    const defaultColDef = useMemo<ColDef>(() => ({
        filter: 'agTextColumnFilter',
        filterParams: {
            filterOptions: ['contains'],
            maxNumConditions: 1
        },
        floatingFilter: true,
        sortable: true,
    }), []);

    const getServerSideDatasource = (): IDatasource => {
        return {
            getRows: async (params) => {
                const startRow = params.startRow ?? 0;
                const endRow = params.endRow ?? 100;
                const pageSize = endRow - startRow;
                const page = Math.floor(startRow / pageSize) + 1;
                setCurrentPage(page);
                try {
                    setLoading(true);

                    const paginationParams: DevicePaginationParams = {
                        page: page.toString(),
                        per_page: pageSize.toString(),
                        filterModel: params.filterModel,
                        orderBy: params.sortModel?.[0]?.colId || '',
                        order: (params.sortModel?.[0]?.sort?.toUpperCase() as 'ASC' | 'DESC') || ''
                    };

                    const response = await getDeviceActivePagination(paginationParams);

                    const rowData = response.data.devices.data;
                    const totalRows = response.data.devices.total;

                    setRowCount(totalRows);
                    params.successCallback(rowData, totalRows);

                    setFailedPages(prev => {
                        const newSet = new Set(prev);
                        newSet.delete(page);
                        return newSet;
                    });

                } catch (error) {
                    let errorMessage = 'Network error occurred while fetching data. Please use the "Retry Failed Loads" button to attempt recovery.';
                    if (axios.isAxiosError(error)) {
                        const statusCode = error.response?.status;
                        if (statusCode === 403 || statusCode === 401) {
                            logout();
                            errorMessage = 'Your session has expired. Please log in again.';
                        } else if (statusCode === 500) {
                            errorMessage = 'An error occurred while fetching data. Please contact admin.';
                        }
                    }
                    console.error('Error fetching data:', error);
                    toast.error(errorMessage);
                    params.failCallback();
                    setFailedPages(prev => new Set(prev).add(page));
                } finally {
                    setLoading(false);
                }
            }
        };
    };

    const onGridReady = useCallback((params: GridReadyEvent) => {
        const datasource = getServerSideDatasource();
        datasourceRef.current = datasource;
        params.api.setGridOption('datasource', datasource);
    }, []);

    const onBtRetry = useCallback(() => {
        if (gridRef.current && datasourceRef.current) {
            setFailedPages(new Set());
            gridRef.current.api.refreshInfiniteCache();
            gridRef.current.api.paginationGoToPage(0);
        }
    }, []);

    const onBtnExport = useCallback(() => {
        if (gridRef.current && gridRef.current.api) {
            const params = {
                fileName: 'device_export.csv',
                columnKeys: gridRef.current.api.getColumnDefs()
                    ?.filter((colDef: ColDef) => colDef.field !== 'DEVICE_ID_crypt' && colDef.headerName !== 'Actions')
                    .map((colDef: ColDef) => colDef.field)
                    .filter((field): field is string => field !== undefined)
            };
            gridRef.current.api.exportDataAsCsv(params);
        }
    }, []);

    const openCreateModal = () => {
        if (createModalRef.current) {
            createModalRef.current.openModal();
        }
    };

    const openUploadModal = () => {
        if (uploadModalRef.current) {
            uploadModalRef.current.openModal();
        }
    };

    React.useEffect(() => {
        if (agGridRefresh && gridRef.current && gridRef.current.api) {
            gridRef.current.api.refreshInfiniteCache();
            setAgGridRefresh(false);
        }
    }, [agGridRefresh]);

    return (
        <>
            <div className="row align-items-center mx-1">
                <div className="col-sm">
                    <h1 className="h5 fw-semibold text-body">Devices</h1>
                    <p className="mt-2 small text-muted">
                        A list of all devices in your account including their details and status.
                    </p>
                </div>

                <div className="col-sm-auto mt-4 mt-sm-0">
                    <button type="button" className="btn btn-primary" onClick={openCreateModal}>
                        Add Device
                    </button>
                </div>
                <div className="col-sm-auto mt-4 mt-sm-0">
                    <button type="button" className="btn btn-light" onClick={() => uploadModalRef.current?.downloadTemplate()}>
                        Device Format
                    </button>
                </div>
                <div className="col-sm-auto mt-4 mt-sm-0">
                    <button type="button" className="btn btn-secondary" onClick={openUploadModal}>
                        Upload
                    </button>
                </div>
                <div className="col-sm-auto mt-4 mt-sm-0">
                    <button type="button" className="btn btn-primary" onClick={onBtnExport}>
                        Export to CSV
                    </button>
                </div>
                <div className="col-sm-auto mt-4 mt-sm-0">
                    {failedPages.size > 0 && (
                        <button className="btn btn-danger" onClick={onBtRetry}>
                            Retry Failed Loads ({failedPages.size} failed)
                        </button>
                    )}
                </div>
            </div>

            <div className="ag-theme-quartz" style={{height: 600, width: '100%'}}>
                <AgGridReact
                    ref={gridRef}
                    columnDefs={columnDefs}
                    defaultColDef={defaultColDef}
                    rowModelType={'infinite'}
                    pagination={true}
                    paginationPageSize={20}
                    onGridReady={onGridReady}
                    noRowsOverlayComponent={NoRowsOverlay}
                    loadingOverlayComponent={LoadingOverlay}
                    loading={loading}
                />
            </div>
            <CreateDeviceModal
                ref={createModalRef}
                id="createDeviceModal"
                title="Create Device"
                setAgGridRefresh={setAgGridRefresh}
            />
            <DeleteDeviceModal
                ref={deleteModalRef}
                id="deleteDeviceModal"
                title="Delete Device"
                device={selectedDevice}
                setAgGridRefresh={setAgGridRefresh}
            />
            <ViewDeviceModal
                ref={viewModalRef}
                id="viewDeviceModal"
                title="View Device"
                device={selectedDevice}
            />
            <UploadDeviceModal
                ref={uploadModalRef}
                id="uploadDeviceModal"
                title="Upload Devices"
                setAgGridRefresh={setAgGridRefresh}
            />
        </>
    );
};

export default DeviceAGGrid;
