export interface DevicePreviewRow {
  DEVICE_MODEL: string;
  IMEI_NO_1: string;
  IMEI_NO_2?: string;
  errors?: {
    device_model?: boolean;
    imei1?: boolean;
    imei2?: boolean;
    duplicate?: boolean;
  };
}

export interface DevicePreviewResponse {
  success: boolean;
  status: number;
  columns?: string[];
  rows?: DevicePreviewRow[];
  counts?: { success: number; failure: number };
  message?: string;
}

export interface DeviceUploadResponse {
  success: boolean;
  status: number;
  message?: string;
  inserted?: number;
  duplicates?: string[];
}

