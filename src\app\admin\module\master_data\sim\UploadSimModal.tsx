import React, {forwardRef, useImperativeHandle, useRef, useState, useMemo} from 'react';
import {Modal} from 'bootstrap';
import {toast} from 'react-toastify';
import axios from "axios";
import {useAuth} from "../../../../modules/auth";
import {simDataPreview, uploadSim} from "../../../../modules/auth/core/_sim_requests";
import {AgGridReact} from 'ag-grid-react';
import {ColDef} from 'ag-grid-community';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import 'ag-grid-community/styles/ag-theme-quartz.css';

interface ModalProps {
    id: string;
    title: string;
    setAgGridRefresh: React.Dispatch<React.SetStateAction<boolean>>;
}

export interface UploadModalRef {
    openModal: () => void;
    closeModal: () => void;
}

interface PreviewData {
    line_number: number;
    SimMobileNo: string;
    SimSerialNo: string;
    SimProvider: string;
    errors: Record<string, string>;
    is_valid: boolean;
}

interface ValidationSummary {
    total_rows: number;
    success_count: number;
    error_count: number;
    can_upload: boolean;
}

const UploadSimModal = forwardRef<UploadModalRef, ModalProps>((props, ref) => {
    const {id, title, setAgGridRefresh} = props;
    const modalRef = useRef<HTMLDivElement>(null);
    const fileInputRef = useRef<HTMLInputElement>(null);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [error, setError] = useState<string | null>(null);
    const [selectedFile, setSelectedFile] = useState<File | null>(null);
    const [previewData, setPreviewData] = useState<PreviewData[]>([]);
    const [validationSummary, setValidationSummary] = useState<ValidationSummary | null>(null);
    const [showPreview, setShowPreview] = useState<boolean>(false);
    const [isDragOver, setIsDragOver] = useState<boolean>(false);
    const [isUploading, setIsUploading] = useState<boolean>(false);
    const {logout} = useAuth();

    const columnDefs = useMemo<ColDef[]>(() => [
        {field: 'line_number', headerName: 'S.No', width: 80},
        {
            field: 'SimMobileNo', 
            headerName: 'Mobile No',
            cellClass: (params) => params.data?.errors?.mobile ? 'text-danger' : ''
        },
        {
            field: 'SimSerialNo', 
            headerName: 'Serial No',
            cellClass: (params) => params.data?.errors?.serial ? 'text-danger' : ''
        },
        {
            field: 'SimProvider', 
            headerName: 'Provider',
            cellClass: (params) => params.data?.errors?.provider ? 'text-danger' : ''
        },
    ], []);

    const defaultColDef = useMemo<ColDef>(() => ({
        filter: 'agTextColumnFilter',
        filterParams: {
            filterOptions: ['contains'],
            maxNumConditions: 1
        },
        floatingFilter: true,
        sortable: true,
        resizable: true,
        flex: 1
    }), []);

    const handleFileSelect = (file: File) => {
        if (file.name !== 'sim_master.csv') {
            toast.error('Please select a file named "sim_master.csv"');
            return;
        }
        setSelectedFile(file);
        setError(null);
    };

    const handleFileInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        const file = event.target.files?.[0];
        if (file) {
            handleFileSelect(file);
        }
    };

    const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        setIsDragOver(true);
    };

    const handleDragLeave = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        setIsDragOver(false);
    };

    const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
        event.preventDefault();
        setIsDragOver(false);
        
        const files = event.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    };

    const handlePreview = async () => {
        if (!selectedFile) {
            toast.error('Please select a file first');
            return;
        }

        setIsLoading(true);
        setError(null);

        const formData = new FormData();
        formData.append('import_file', selectedFile);

        try {
            const response = await simDataPreview(formData);

            if (response.data.success) {
                setPreviewData(response.data.data.rows);
                console.log('previewData',previewData);
                setValidationSummary(response.data.data.summary);
                setShowPreview(true);
                toast.success('File processed successfully');
            } else {
                setError(response.data.message || 'Failed to process file');
                toast.error(response.data.message || 'Failed to process file');
            }
        } catch (error) {
            let errorMessage = 'An error occurred while processing the file. Please try again.';
            if (axios.isAxiosError(error)) {
                const statusCode = error.response?.status;
                if (statusCode === 403 || statusCode === 401) {
                    closeModal();
                    logout();
                    errorMessage = 'Your session has expired. Please log in again.';
                } else if (statusCode === 500) {
                    errorMessage = 'An error occurred while processing the file. Please contact admin.';
                } else if (error.response?.data?.message) {
                    errorMessage = error.response.data.message;
                }
            }
            setError(errorMessage);
            toast.error(errorMessage);
            console.error("Error previewing file:", error);
        } finally {
            setIsLoading(false);
        }
    };

    const handleUpload = async () => {
        if (!previewData.length || !validationSummary?.can_upload) {
            toast.error('No valid data to upload');
            return;
        }

        setIsUploading(true);
        setError(null);

        try {
            const response = await uploadSim(previewData);

            if (response.data.success) {
                toast.success(response.data.message);
                setAgGridRefresh(true);
                closeModal();
                resetForm();
            } else {
                setError(response.data.message || 'Failed to upload data');
                toast.error(response.data.message || 'Failed to upload data');
            }
        } catch (error) {
            let errorMessage = 'An error occurred while uploading data. Please try again.';
            if (axios.isAxiosError(error)) {
                const statusCode = error.response?.status;
                if (statusCode === 403 || statusCode === 401) {
                    closeModal();
                    logout();
                    errorMessage = 'Your session has expired. Please log in again.';
                } else if (statusCode === 500) {
                    errorMessage = 'An error occurred while uploading data. Please contact admin.';
                } else if (error.response?.data?.message) {
                    errorMessage = error.response.data.message;
                }
            }
            setError(errorMessage);
            toast.error(errorMessage);
            console.error("Error uploading data:", error);
        } finally {
            setIsUploading(false);
        }
    };

    const resetForm = () => {
        setSelectedFile(null);
        setPreviewData([]);
        setValidationSummary(null);
        setShowPreview(false);
        setError(null);
        setIsLoading(false);
        setIsUploading(false);
        if (fileInputRef.current) {
            fileInputRef.current.value = '';
        }
    };

    const openModal = () => {
        const modalElement = document.getElementById(id);
        if (modalElement) {
            const modalInstance = Modal.getInstance(modalElement);
            if (modalInstance) {
                modalInstance.show();
            } else {
                new Modal(modalElement).show();
            }
        }
    };

    const closeModal = () => {
        const modalElement = document.getElementById(id);
        if (modalElement) {
            const modalInstance = Modal.getInstance(modalElement);
            if (modalInstance) {
                modalInstance.hide();
            }
        }
        resetForm();
    };

    useImperativeHandle(ref, () => ({
        openModal,
        closeModal
    }));

    return (
        <div className="modal fade" tabIndex={-1} id={id} ref={modalRef}>
            <div className="modal-dialog modal-xl">
                <div className="modal-content">
                    <div className="modal-header">
                        <h5 className="modal-title">{title}</h5>
                        <button
                            type="button"
                            className="btn-close"
                            data-bs-dismiss="modal"
                            aria-label="Close"
                            onClick={closeModal}
                        ></button>
                    </div>
                    
                    {error ? (
                        <>
                            <div className="modal-body">
                                <div className="text-center">
                                    <p className="text-danger mb-3">{error}</p>
                                </div>
                            </div>
                            <div className="modal-footer">
                                <button
                                    type="button"
                                    className="btn btn-light"
                                    onClick={closeModal}
                                    disabled={isLoading || isUploading}>
                                    Cancel
                                </button>
                                <button
                                    className="btn btn-primary"
                                    onClick={() => setError(null)}
                                    disabled={isLoading || isUploading}>
                                    Try Again
                                </button>
                            </div>
                        </>
                    ) : (
                        <>
                            <div className="modal-body">
                                {!showPreview ? (
                                    <div>
                                        <h6 className="fw-bold mb-3">Upload Your File</h6>
                                        <div 
                                            className={`border-2 border-dashed rounded p-5 text-center ${
                                                isDragOver ? 'border-primary bg-light' : 'border-muted'
                                            }`}
                                            onDragOver={handleDragOver}
                                            onDragLeave={handleDragLeave}
                                            onDrop={handleDrop}
                                        >
                                            <input
                                                type="file"
                                                ref={fileInputRef}
                                                onChange={handleFileInputChange}
                                                accept=".csv"
                                                style={{display: 'none'}}
                                            />
                                            <button
                                                type="button"
                                                className="btn btn-outline-secondary mb-2"
                                                onClick={() => fileInputRef.current?.click()}
                                                disabled={isLoading}
                                            >
                                                Choose file
                                            </button>
                                            <p className="text-muted mb-2">
                                                {selectedFile ? selectedFile.name : 'No file chosen'}
                                            </p>
                                            <p className="text-info small">Or Drag It Here.</p>
                                        </div>
                                    </div>
                                ) : (
                                    <div>
                                        <div className="d-flex justify-content-between align-items-center mb-3">
                                            <h6 className="fw-bold mb-0">Data Preview</h6>
                                            <div className="text-end">
                                                <span className="me-3">
                                                    <strong>Showing 1 to {previewData.length} of {previewData.length} entries</strong>
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <div className="ag-theme-quartz mb-3" style={{height: 300, width: '100%'}}>
                                            <AgGridReact
                                                columnDefs={columnDefs}
                                                rowData={previewData}
                                                defaultColDef={defaultColDef}
                                                pagination={true}
                                                paginationPageSize={10}
                                                paginationPageSizeSelector={[10, 20, 50]}
                                            />
                                        </div>

                                        {validationSummary && (
                                            <div className="text-center">
                                                <span className="text-primary me-3">
                                                    <strong>Failure {validationSummary.error_count}</strong>
                                                </span>
                                                <span className="text-success">
                                                    <strong>Success {validationSummary.success_count}</strong>
                                                </span>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>
                            
                            <div className="modal-footer">
                                <button
                                    type="button"
                                    className="btn btn-light"
                                    onClick={closeModal}
                                    disabled={isLoading || isUploading}>
                                    Cancel
                                </button>
                                
                                {!showPreview ? (
                                    <button
                                        className="btn btn-success"
                                        onClick={handlePreview}
                                        disabled={!selectedFile || isLoading}
                                    >
                                        {!isLoading && <span className="indicator-label">Preview</span>}
                                        {isLoading && (
                                            <span className='indicator-progress' style={{display: 'block'}}>
                                                Please wait...
                                                <span className='spinner-border spinner-border-sm align-middle ms-2'></span>
                                            </span>
                                        )}
                                    </button>
                                ) : (
                                    <>
                                        <button
                                            className="btn btn-secondary"
                                            onClick={() => {
                                                setShowPreview(false);
                                                setPreviewData([]);
                                                setValidationSummary(null);
                                            }}
                                            disabled={isLoading || isUploading}
                                        >
                                            Back
                                        </button>
                                        {validationSummary?.can_upload && (
                                            <button
                                                className="btn btn-success"
                                                onClick={handleUpload}
                                                disabled={!validationSummary?.can_upload || isUploading}
                                            >
                                                {!isUploading && <span className="indicator-label">Upload</span>}
                                                {isUploading && (
                                                    <span className='indicator-progress' style={{display: 'block'}}>
                                                        Uploading...
                                                        <span className='spinner-border spinner-border-sm align-middle ms-2'></span>
                                                    </span>
                                                )}
                                            </button>
                                        )}
                                    </>
                                )}
                            </div>
                        </>
                    )}
                </div>
            </div>
        </div>
    );
});

export default UploadSimModal;