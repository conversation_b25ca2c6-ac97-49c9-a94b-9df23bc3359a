import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { Modal } from 'bootstrap';

export interface EmployeeIDChangeModalRef {
  openModal: () => void;
  closeModal: () => void;
}

interface EmployeeIDChangeModalProps {
  id: string;
  title: string;
  oldEmployeeId: string;
  onSubmit: (newEmployeeId: string) => void;
  loading?: boolean;
  error?: string | null;
  success?: boolean;
}

const EmployeeIDChangeModal = forwardRef<EmployeeIDChangeModalRef, EmployeeIDChangeModalProps>(
  ({ id, title, oldEmployeeId, onSubmit, loading = false, error = null, success = false }, ref) => {
    const modalRef = useRef<HTMLDivElement>(null);
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [newEmployeeId, setNewEmployeeId] = useState('');
    React.useEffect(() => {
      const modalElement = modalRef.current;
      if (modalElement) {
        new Modal(modalElement);
        modalElement.addEventListener('show.bs.modal', handleModalOpen);
        modalElement.addEventListener('hidden.bs.modal', handleModalClose);
        return () => {
          modalElement.removeEventListener('show.bs.modal', handleModalOpen);
          modalElement.removeEventListener('hidden.bs.modal', handleModalClose);
        };
      }
    }, []);

    React.useEffect(() => {
      // Only close modal on success
      if (success && isModalOpen) {
        closeModal();
      }
    }, [success]);

    const openModal = () => {
      setNewEmployeeId('');
      const modalElement = document.getElementById(id);
      if (modalElement instanceof HTMLElement) {
        const modalInstance = Modal.getInstance(modalElement);
        if (modalInstance) {
          modalInstance.show();
        } else {
          new Modal(modalElement).show();
        }
      }
    };

    const closeModal = () => {
      const modalElement = document.getElementById(id);
      if (modalElement instanceof HTMLElement) {
        const modalInstance = Modal.getInstance(modalElement);
        if (modalInstance) {
          modalInstance.hide();
        }
      }
    };

    useImperativeHandle(ref, () => ({
      openModal,
      closeModal,
    }));

    const handleModalOpen = () => {
      setIsModalOpen(true);
    };
    const handleModalClose = () => {
      setIsModalOpen(false);
    };

    const handleSubmit = () => {
      onSubmit(newEmployeeId);
    };

    return (
      <>
        {isModalOpen && (
          <div className="modal-backdrop fade show" style={{ zIndex: 1056 }}></div>
        )}
        <div className="modal fade" style={{ zIndex: 1060 }} tabIndex={-1} id={id} ref={modalRef}>
          <div className="modal-dialog">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">{title}</h5>
                <button
                  type="button"
                  className="btn-close"
                  data-bs-dismiss="modal"
                  aria-label="Close"
                ></button>
              </div>
              <div className="modal-body">
                <div className="mb-3 row align-items-center">
                  <label className="col-sm-4 col-form-label fw-bold">Old Employee ID :</label>
                  <div className="col-sm-8">
                    <input
                      type="text"
                      className="form-control"
                      value={oldEmployeeId}
                      readOnly
                      style={{ backgroundColor: '#eee' }}
                    />
                  </div>
                </div>
                <div className="mb-3 row align-items-center">
                  <label className="col-sm-4 col-form-label fw-bold">New Employee ID:</label>
                  <div className="col-sm-8">
                    <input
                      type="text"
                      className="form-control"
                      value={newEmployeeId}
                      onChange={e => setNewEmployeeId(e.target.value)}
                      placeholder="Enter new Employee ID"
                      disabled={loading}
                    />
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button type="button" className="btn btn-light" onClick={closeModal} disabled={loading}>
                  Close
                </button>
                <button type="button" className="btn btn-primary" onClick={handleSubmit} disabled={loading}>
                  {!loading && 'Submit'}
                  {loading && (
                    <span className="indicator-progress" style={{ display: 'block' }}>
                      <span className="spinner-border spinner-border-sm align-middle ms-2"></span>
                    </span>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </>
    );
  }
);

export default EmployeeIDChangeModal;