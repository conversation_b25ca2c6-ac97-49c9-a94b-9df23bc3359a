import React, {useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {AgGridReact} from 'ag-grid-react';
import {ColDef, GridReadyEvent, ICellRendererParams, IDatasource} from 'ag-grid-community';
import 'ag-grid-community/styles/ag-grid.css';
import 'ag-grid-community/styles/ag-theme-alpine.css';
import 'ag-grid-community/styles/ag-theme-quartz.css';
import {ActiveEmployeePaginationParams, useAuth} from "../../../../../modules/auth";
import {activeEmployeePagination} from "../../../../../modules/auth/core/_requests.ts";
import {Employee, ModalOne, ModalOneRef} from "../../../../../Helper/HelperOne.tsx";
import {EditEmployee} from "./EditEmployee.tsx";
import {DeleteEmployee} from "./DeleteEmployee.tsx";
import {ViewEmployee} from "./ViewEmployee.tsx";
import CreateEmployee from "./CreateEmployee.tsx";
import '../../../../../custom_css/AGGridButtonStyle.css';
import {LoadingOverlay, NoRowsOverlay} from "../../../../../Helper/AgGridOverlays.tsx";
import {toast} from 'react-toastify';
import axios from "axios";
import EmployeeIDChangeModal, { EmployeeIDChangeModalRef } from './EmployeeIDChangeModal';
import { updateEmployeeId } from '../../../../../modules/auth/core/_requests';
import type { EmployeeIdUpdateRequest } from '../../../../../modules/auth/core/_models';

const EmployeeAGGrid: React.FC = () => {
    const [agGridRefresh, setAgGridRefresh] = useState(false);
    const gridRef = useRef<AgGridReact>(null);
    const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null);
    const modalRef = useRef<ModalOneRef>(null);
    const deleteModalRef = useRef<ModalOneRef>(null);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const viewModalRef = useRef<ModalOneRef>(null);
    const [loading, setLoading] = useState(true);
    const [rowCount, setRowCount] = useState<number | null>(null);
    const datasourceRef = useRef<IDatasource | null>(null);
    const [failedPages, setFailedPages] = useState<Set<number>>(new Set());
    const [resetFormData, setResetFormData] = useState(false);
    const [currentPage, setCurrentPage] = useState(1);
    const [currentSortModel, setCurrentSortModel] = useState<any[]>([]);
    const [currentFilterModel, setCurrentFilterModel] = useState<any>({});
    const [paginationPageSizeSelector] = useState([20, 50, 100]);
    const [isExporting, setIsExporting] = useState(false);
    const {logout} = useAuth();
    const [employeeIdForChange, setEmployeeIdForChange] = useState<string | null>(null);
    const employeeIdChangeModalRef = useRef<EmployeeIDChangeModalRef>(null);
    const [idChangeLoading, setIdChangeLoading] = useState(false);
    const [idChangeError, setIdChangeError] = useState<string | null>(null);
    const [idChangeSuccess, setIdChangeSuccess] = useState(false);

    useEffect(() => {
        if (selectedEmployee && isDeleteModalOpen && deleteModalRef.current) {
            deleteModalRef.current.openModal();
            setIsDeleteModalOpen(false);
        }
    }, [selectedEmployee, isDeleteModalOpen]);

    useEffect(() => {
        if (agGridRefresh && gridRef.current && gridRef.current.api) {
            gridRef.current.api.refreshInfiniteCache();
            setAgGridRefresh(false);
        }
    }, [agGridRefresh, setAgGridRefresh]);

    useEffect(() => {
        if (!loading && gridRef.current && gridRef.current.api) {
            try {
                if (rowCount === 0 && currentPage === 1) {
                    gridRef.current.api.showNoRowsOverlay();
                } else {
                    gridRef.current.api.hideOverlay();
                }
            } catch (error) {
                console.error('Error updating grid overlay:', error);
            }
        }
    }, [loading, rowCount, currentPage]);

    const ActionCellRenderer: React.FC<ICellRendererParams> = (props) => {
        const onEdit = () => {
            setSelectedEmployee(props.data);
            if (modalRef.current) {
                modalRef.current.openModal();
            }
        };

        const onDelete = () => {
            setSelectedEmployee(props.data);
            setIsDeleteModalOpen(true);
        };

        const onView = () => {
            setSelectedEmployee(props.data);
            if (viewModalRef.current) {
                viewModalRef.current.openModal();
            }
        };

        return (
            <div className="d-flex align-items-center mt-2 gap-1">
                <button onClick={onEdit} className="btn btn-sm action-btn text-success p-1" title="Edit"
                        style={{width: '36px'}}>
                    <i className="fas fa-edit text-success"></i>
                </button>
                <button onClick={onDelete} className="btn btn-sm action-btn text-danger p-1" title="Delete"
                        style={{width: '36px'}}>
                    <i className="fas fa-trash-alt text-danger"></i>
                </button>
                <button onClick={onView} className="btn btn-sm action-btn text-primary p-1" title="View"
                        style={{width: '36px'}}>
                    <i className="fas fa-eye text-success"></i>
                </button>
            </div>
        );
    };

    // Custom renderer for Employee ID column with edit button
    const EmployeeIdCellRenderer: React.FC<ICellRendererParams> = (props) => {
        const handleEditClick = () => {
            setEmployeeIdForChange(props.value);
            employeeIdChangeModalRef.current?.openModal();
        };
        return (
            <div className="d-flex align-items-center gap-2">
                <button
                    className="btn btn-sm action-btn text-success p-1"
                    title="Edit Employee ID"
                    style={{ width: '32px' }}
                    onClick={handleEditClick}
                >
                    <i className="fas fa-edit text-success"></i>
                </button>
                <span>{props.value}</span>
            </div>
        );
    };

    const columnDefs = useMemo<ColDef[]>(() => [
        {
            field: 'EMPLOYEES_ID',
            headerName: 'Employee ID',
            cellRenderer: EmployeeIdCellRenderer,
        },
        {field: 'name_decrypted', headerName: 'Name', sortable: false},
        {field: 'mobile_decrypted', headerName: 'Mobile', sortable: false},
        {field: 'PROJECT_NAME', headerName: 'Project Name'},
        {field: 'GENDER', headerName: 'Gender'},
        {field: 'BRANCH_NAME', headerName: 'Branch Name'},
        {field: 'primary_addr', headerName: 'Primary Address'},
        {field: 'secondary_addr', headerName: 'Secondary Address'},
        {field: 'email_decrypted', headerName: 'Email', sortable: false},
        {
            headerName: 'Actions',
            cellRenderer: ActionCellRenderer,
            sortable: false,
            filter: false,
        },
    ], []);

    const defaultColDef = useMemo<ColDef>(() => ({
        filter: 'agTextColumnFilter',
        filterParams: {
            filterOptions: ['contains'],
            maxNumConditions: 1
        },
        floatingFilter: true,
        sortable: true,
    }), []);

    const getServerSideDatasource = (): IDatasource => {
        return {
            getRows: async (params) => {
                const startRow = params.startRow ?? 0;
                const endRow = params.endRow ?? 100;
                const pageSize = endRow - startRow;
                const page = Math.floor(startRow / pageSize) + 1;
                setCurrentPage(page);

                try {
                    setLoading(true);


                    setCurrentFilterModel(params.filterModel || {});
                    setCurrentSortModel(params.sortModel || []);

                    const paginationParams: ActiveEmployeePaginationParams = {
                        page: page.toString(),
                        per_page: pageSize.toString(),
                        filterModel: params.filterModel,
                        orderBy: params.sortModel?.[0]?.colId || '',
                        order: (params.sortModel?.[0]?.sort?.toUpperCase() as 'ASC' | 'DESC') || ''
                    };

                    const response = await activeEmployeePagination(paginationParams);

                    const rowData = response.data.employees.data;
                    const totalRows = response.data.employees.total;

                    setRowCount(totalRows);
                    params.successCallback(rowData, totalRows);

                    setFailedPages(prev => {
                        const newSet = new Set(prev);
                        newSet.delete(page);
                        return newSet;
                    });

                } catch (error) {
                    let errorMessage = 'Network error occurred while fetching data. Please use the "Retry Failed Loads" button to attempt recovery.';
                    if (axios.isAxiosError(error)) {
                        const statusCode = error.response?.status;
                        if (statusCode === 403 || statusCode === 401) {
                            logout();
                            errorMessage = 'Your session has expired. Please log in again.';
                        } else if (statusCode === 500) {
                            errorMessage = 'An error occurred while fetching data. Please contact admin.';
                        }
                    }
                    console.error('Error fetching data:', error);
                    toast.error(errorMessage);
                    params.failCallback();
                    setFailedPages(prev => new Set(prev).add(page));
                } finally {
                    setLoading(false);
                }
            }
        };
    };

    const onGridReady = useCallback((params: GridReadyEvent) => {
        const datasource = getServerSideDatasource();
        datasourceRef.current = datasource;
        params.api.setGridOption('datasource', datasource);
    }, []);

    const onBtRetry = useCallback(() => {
        if (gridRef.current && datasourceRef.current) {
            setFailedPages(new Set());
            gridRef.current.api.refreshInfiniteCache();
            gridRef.current.api.paginationGoToPage(0);
        }
    }, []);

    const onBtnExport = useCallback(async () => {
        try {
            setIsExporting(true);
            toast.info('Preparing export... Please wait.');


            const exportParams: ActiveEmployeePaginationParams = {
                page: '1',
                per_page: (rowCount || 999999).toString(),
                filterModel: currentFilterModel,
                orderBy: currentSortModel[0]?.colId || '',
                order: (currentSortModel[0]?.sort?.toUpperCase() as 'ASC' | 'DESC') || ''
            };

            const response = await activeEmployeePagination(exportParams);
            const allData = response.data.employees.data;

            if (allData.length === 0) {
                toast.warning('No data available to export.');
                return;
            }


            const headers = columnDefs
                .filter(col => col.field && col.field !== 'id_encrypted' && col.headerName !== 'Actions')
                .map(col => col.headerName)
                .join(',');

            const csvContent = [
                headers,
                ...allData.map(row =>
                    columnDefs
                        .filter(col => col.field && col.field !== 'id_encrypted' && col.headerName !== 'Actions')
                        .map(col => {
                            const value = row[col.field as keyof typeof row] || '';
                            return typeof value === 'string' && (value.includes(',') || value.includes('"'))
                                ? `"${value.replace(/"/g, '""')}"`
                                : value;
                        })
                        .join(',')
                )
            ].join('\n');


            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `employees_export_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            toast.success(`Successfully exported ${allData.length} employee records.`);

        } catch (error) {
            let errorMessage = 'Export failed. Please try again.';
            if (axios.isAxiosError(error)) {
                const statusCode = error.response?.status;
                if (statusCode === 403 || statusCode === 401) {
                    logout();
                    errorMessage = 'Your session has expired. Please log in again.';
                } else if (statusCode === 500) {
                    errorMessage = 'An error occurred while fetching data. Please contact admin.';
                }
            }
            console.error('Export failed:', error);
            toast.error(errorMessage);
        } finally {
            setIsExporting(false);
        }
    }, [columnDefs, rowCount, currentFilterModel, currentSortModel]);

    const handleAddEmployeeClick = () => {
        setResetFormData(true);
    };

    // Handler for Employee ID Change Modal submit
    const handleEmployeeIdChangeSubmit = async (newEmployeeId: string) => {
        if (!employeeIdForChange) return;
        setIdChangeLoading(true);
        setIdChangeError(null);
        setIdChangeSuccess(false);
        try {
            const req: EmployeeIdUpdateRequest = {
                oldempid: employeeIdForChange,
                newempid: newEmployeeId,
            };
            const response = await updateEmployeeId(req);
            if (response.data.success) {
                setIdChangeError(null);
                setIdChangeSuccess(true);
                toast.success(response.data.message || 'Employee ID updated successfully!');
                setAgGridRefresh(true);
            } else {
                const errorMessage = response.data.message || 'Failed to update Employee ID.';
                setIdChangeError(errorMessage);
                setIdChangeSuccess(false);
                toast.error(errorMessage);
            }
        } catch (error) {
            let errorMessage = 'Network error occurred while updating Employee ID. Please try again.';
            if (axios.isAxiosError(error)) {
                const statusCode = error.response?.status;
                if (statusCode === 403 || statusCode === 401) {
                    logout();
                    errorMessage = 'Your session has expired. Please log in again.';
                } else if (statusCode === 500) {
                    errorMessage = 'An error occurred while updating Employee ID. Please contact admin.';
                }
            }
            setIdChangeError(errorMessage);
            setIdChangeSuccess(false);
            toast.error(errorMessage);
        } finally {
            setIdChangeLoading(false);
        }
    };



    return (
        <>
            <div className="row align-items-center mx-1">
                <div className="col-sm">
                    <h1 className="h5 fw-semibold text-body">Active Employee</h1>
                    <p className="mt-2 small text-muted">
                        A list of all the active employees in your account, including their name, email, and other
                        details.
                    </p>
                </div>
                <div className="col-sm-auto mt-4 mt-sm-0">
                    <button type="button"
                            className="btn btn-primary"
                            data-bs-toggle="modal"
                            data-bs-target="#kt_modal_1"
                            onClick={handleAddEmployeeClick}>
                        Add employee
                    </button>
                </div>
                <div className="col-sm-auto mt-4 mt-sm-0">
                    <button
                        type="button"
                        className="btn btn-primary"
                        onClick={onBtnExport}
                        disabled={isExporting}>
                        {!isExporting && <span className="indicator-label">Export to CSV</span>}
                        {isExporting && (
                            <span className='indicator-progress' style={{display: 'block'}}>
                                Exporting...
                                <span className='spinner-border spinner-border-sm align-middle ms-2'></span>
                            </span>
                        )}
                    </button>
                </div>
                <div className="col-sm-auto mt-4 mt-sm-0">
                    {failedPages.size > 0 && (
                        <button
                            className="btn btn-danger"
                            onClick={onBtRetry}>
                            Retry Failed Loads ({failedPages.size} failed)
                        </button>
                    )}
                </div>
            </div>

            <div className="ag-theme-quartz" style={{height: 600, width: '100%'}}>
                <AgGridReact
                    ref={gridRef}
                    columnDefs={columnDefs}
                    defaultColDef={defaultColDef}
                    rowModelType={'infinite'}
                    pagination={true}
                    paginationPageSize={20}
                    onGridReady={onGridReady}
                    paginationPageSizeSelector={paginationPageSizeSelector}
                    noRowsOverlayComponent={NoRowsOverlay}
                    loadingOverlayComponent={LoadingOverlay}
                    loading={loading}
                />
            </div>
            <ModalOne
                ref={modalRef}
                modalId="editEmployeeModal"
                title="Edit Employee">
                {({closeModal}) => (
                    selectedEmployee && (
                        <EditEmployee
                            employee={selectedEmployee}
                            closeModal={closeModal}
                            onEmployeeUpdated={() => setAgGridRefresh(true)}
                        />
                    )
                )}
            </ModalOne>

            {selectedEmployee && (
                <DeleteEmployee
                    employee={selectedEmployee}
                    ref={deleteModalRef}
                    modalId="deleteEmployeeModal"
                    title="Confirm Deletion"
                    message="Are you sure you want to delete this employee?"
                    setAgGridRefresh={setAgGridRefresh}
                />
            )}

            <ModalOne
                ref={viewModalRef}
                modalId="viewEmployeeModal"
                title="View Employee">
                {() => (
                    selectedEmployee && (
                        <ViewEmployee
                            employee={selectedEmployee}
                        />
                    )
                )}
            </ModalOne>

            <ModalOne modalId="kt_modal_1" title="Create Employee">
                {({closeModal}) => (
                    <CreateEmployee
                        resetFormData={resetFormData}
                        onResetFormData={() => setResetFormData(false)}
                        closeModal={closeModal}
                        setAgGridRefresh={setAgGridRefresh}
                    />
                )}
            </ModalOne>

            <EmployeeIDChangeModal
                ref={employeeIdChangeModalRef}
                id="employeeIdChangeModal"
                title="Employee ID Change"
                oldEmployeeId={employeeIdForChange || ''}
                onSubmit={handleEmployeeIdChangeSubmit}
                loading={idChangeLoading}
                error={idChangeError}
                success={idChangeSuccess}
            />
        </>
    );
};

export default EmployeeAGGrid;