import axios from 'axios';
import {DevicePreviewResponse, DeviceUploadResponse} from './_device_upload_models';

const API_URL = import.meta.env.VITE_APP_API_URL;

export const DEVICE_PREVIEW_URL = `${API_URL}/upload/device/preview`;
export const DEVICE_UPLOAD_URL = `${API_URL}/upload/device`;

export function previewDeviceUpload(formData: FormData) {
  return axios.post<DevicePreviewResponse>(DEVICE_PREVIEW_URL, formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  });
}

export function uploadDevices(payload: { rows: Array<{ DEVICE_MODEL: string; IMEI_NO_1: string; IMEI_NO_2?: string }> }) {
  return axios.post<DeviceUploadResponse>(DEVICE_UPLOAD_URL, payload);
}

