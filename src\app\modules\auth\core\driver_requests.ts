import axios from 'axios';
import {
    CreateDriverParams,
    CreateDriverResponse,
    DeleteDriverParams,
    DeleteDriverResponse,
    DriverDataForCreateResponse,
    DriverPaginationParams,
    DriverPaginationResponse,
    UpdateDriverParams,
    UpdateDriverResponse
} from "./driver_models.ts";


const API_URL = import.meta.env.VITE_APP_API_URL;

export const DRIVER_ACTIVE_PAGINATION_URL = `${API_URL}/driver/active/pagination`;
export const DRIVER_DATA_FOR_CREATE_URL = `${API_URL}/driver/data/for/create`;
export const CREATE_DRIVER_URL = `${API_URL}/driver`;
export const UPDATE_DRIVER_URL = `${API_URL}/driver/update`;
export const DELETE_DRIVER_URL = `${API_URL}/driver/delete`;

export function getActiveDriverPagination(params: DriverPaginationParams) {
    return axios.post<DriverPaginationResponse>(DRIVER_ACTIVE_PAGINATION_URL, params);
}

export function getDriverDataForCreate() {
    return axios.get<DriverDataForCreateResponse>(DRIVER_DATA_FOR_CREATE_URL);
}

export function createDriver(params: CreateDriverParams) {
    const formData = new FormData();

    Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
            if (key === 'driver_image' && value instanceof File) {
                formData.append(key, value, value.name);
            } else {
                formData.append(key, String(value));
            }
        }
    });

    return axios.post<CreateDriverResponse>(CREATE_DRIVER_URL, formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
}

export function updateDriver(idCrypt: string, params: UpdateDriverParams) {
    const formData = new FormData();

    Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
            if (key === 'driver_image' && value instanceof File) {
                formData.append(key, value);
            } else {
                formData.append(key, String(value));
            }
        }
    });

    return axios.post<UpdateDriverResponse>(`${UPDATE_DRIVER_URL}/${idCrypt}`, formData, {
        headers: {
            'Content-Type': 'multipart/form-data',
        },
    });
}

export function deleteDriver(idCrypt: string, params: DeleteDriverParams) {
    return axios.put<DeleteDriverResponse>(`${DELETE_DRIVER_URL}/${idCrypt}`, params);
}